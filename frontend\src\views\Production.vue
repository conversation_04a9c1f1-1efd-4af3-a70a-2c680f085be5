<template>
  <ErpContent v-model="activeTab">
    <template #stats>
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月生产订单数</div>
        <div class="erp-stat-value">{{ totalOrders }}</div>
        <i class="fas fa-industry erp-stat-icon blue"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">进行中工单</div>
        <div class="erp-stat-value">{{ inProgressCount }}</div>
        <i class="fas fa-cogs erp-stat-icon green"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">已完成工单</div>
        <div class="erp-stat-value">{{ completedCount }}</div>
        <i class="fas fa-check-circle erp-stat-icon purple"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">操作</div>
        <button class="btn-blue" @click="addProduction">
          <i class="fas fa-plus-circle"></i>
          <span>新建生产单</span>
        </button>
      </div>
    </template>
    <template #search>
      <!-- 保留一个空的search插槽，因为我们将搜索栏移到toolbar中 -->
    </template>
    <template #toolbar>
      <div class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="输入单号/产品名称搜索" size="small" clearable />
          <el-select v-model="search.status" placeholder="状态筛选" size="small" clearable style="width: 120px; margin-left: 10px;">
            <el-option label="计划中" value="计划中" />
            <el-option label="执行中" value="执行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已暂停" value="已暂停" />
          </el-select>
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
      </div>
    </template>
    <template #tabs>
      <el-tab-pane label="生产管理" name="production">
        <ErpTable
          ref="productionTable"
          :data="filteredProductionList"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
            <el-table-column
              prop="orderNo"
              label="单号"
            />
            <el-table-column
              prop="product"
              label="产品"
            />
            <el-table-column
              prop="quantity"
              label="数量"
            >
              <template #default="scope">
                {{ scope.row.quantity.toLocaleString('zh-CN') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
            >
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="planDate"
              label="计划日期"
            />
            <el-table-column
              prop="actualDate"
              label="实际日期"
            />
            <el-table-column
              prop="progress"
              label="进度"
            >
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.progress"
                  :status="scope.row.progress === 100 ? 'success' : ''"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
            >
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="editProduction(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                  <button
                    v-if="scope.row.status === '计划中'"
                    class="btn-yellow-light compact"
                    @click="startExecution(scope.row)"
                  >
                    <i class="fas fa-play"></i>
                    <span>开始</span>
                  </button>
                  <button
                    v-if="scope.row.status === '执行中'"
                    class="btn-green-light compact"
                    @click="completeProduction(scope.row)"
                  >
                    <i class="fas fa-check"></i>
                    <span>完成</span>
                  </button>
                  <button
                    v-if="scope.row.status === '执行中'"
                    class="btn-orange-light compact"
                    @click="pauseProduction(scope.row)"
                  >
                    <i class="fas fa-pause"></i>
                    <span>暂停</span>
                  </button>
                  <button
                    v-if="scope.row.status === '已暂停'"
                    class="btn-yellow-light compact"
                    @click="resumeProduction(scope.row)"
                  >
                    <i class="fas fa-play"></i>
                    <span>恢复</span>
                  </button>
                </div>
              </template>
            </el-table-column>
        </ErpTable>
      </el-tab-pane>
    </template>
  </ErpContent>

  <!-- 新建/编辑生产单弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogMode === 'add' ? '新建生产单' : '编辑生产单'"
    width="480px"
    align-center
    draggable
  >
    <el-form :model="form" label-width="80px" label-position="right">
      <el-form-item label="单号">
        <el-input v-model="form.orderNo" placeholder="自动生成" :disabled="dialogMode === 'add'" />
      </el-form-item>
      <el-form-item label="产品名称">
        <el-input v-model="form.product" placeholder="请输入产品名称" />
      </el-form-item>
      <el-form-item label="生产数量">
        <el-input v-model="form.quantity" type="number" placeholder="请输入生产数量" />
      </el-form-item>
      <el-form-item label="计划日期">
        <el-date-picker v-model="form.planDate" type="date" placeholder="选择计划日期" style="width: 100%" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">
        <i class="fas fa-times"></i>
        取消
      </el-button>
      <el-button type="primary" @click="saveProduction">
        <i class="fas fa-save"></i>
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import ErpContent from '../components/ErpContent.vue'
import ErpTable from '../components/common/ErpTable.vue'

// 定义生产单数据类型
interface ProductionItem {
  id: number
  orderNo: string
  product: string
  quantity: number
  status: '计划中' | '执行中' | '已完成' | '已暂停'
  planDate: string
  actualDate: string
  progress: number
}

// 定义表单数据类型
interface ProductionForm {
  id: number
  orderNo: string
  product: string
  quantity: number | string
  planDate: string
  remark: string
}

const activeTab = ref('production')
const productionTable = ref()

// 弹窗相关状态
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const form = ref<ProductionForm>({
  id: 0,
  orderNo: '',
  product: '',
  quantity: '',
  planDate: '',
  remark: ''
})

// 统一的生产数据列表
const productionList = ref<ProductionItem[]>([
  {
    id: 1,
    orderNo: 'PROD-001',
    product: 'A产品',
    quantity: 100,
    status: '计划中',
    planDate: '2024-07-05',
    actualDate: '',
    progress: 0
  },
  {
    id: 2,
    orderNo: 'PROD-002-LONG-ORDER-NUMBER',
    product: '高精度数控机床零部件加工产品',
    quantity: 200000,
    status: '执行中',
    planDate: '2024-07-04',
    actualDate: '2024-07-04',
    progress: 65
  },
  {
    id: 3,
    orderNo: 'PROD-003',
    product: 'C产品',
    quantity: 150,
    status: '已完成',
    planDate: '2024-07-01',
    actualDate: '2024-07-03',
    progress: 100
  },
  {
    id: 4,
    orderNo: 'PROD-004',
    product: '超长产品名称测试用例产品',
    quantity: 80,
    status: '已暂停',
    planDate: '2024-07-02',
    actualDate: '2024-07-02',
    progress: 30
  },
  {
    id: 5,
    orderNo: 'PROD-005-VERY-LONG-NUMBER-TEST',
    product: '短名',
    quantity: 1500000,
    status: '计划中',
    planDate: '2024-07-06',
    actualDate: '',
    progress: 0
  }
])

const search = ref({ keyword: '', status: '' })

// 计算属性 - 统计数据
const totalOrders = computed(() => productionList.value.length)
const inProgressCount = computed(() => productionList.value.filter(item => item.status === '执行中').length)
const completedCount = computed(() => productionList.value.filter(item => item.status === '已完成').length)

// 计算属性 - 过滤后的数据
const filteredProductionList = computed(() => {
  let filtered = productionList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.orderNo.toLowerCase().includes(search.value.keyword.toLowerCase()) ||
      item.product.toLowerCase().includes(search.value.keyword.toLowerCase())
    )
  }

  if (search.value.status) {
    filtered = filtered.filter(item => item.status === search.value.status)
  }

  return filtered
})

// 状态标签类型
function getStatusType(status: string) {
  switch (status) {
    case '计划中': return 'info'
    case '执行中': return 'warning'
    case '已完成': return 'success'
    case '已暂停': return 'danger'
    default: return 'info'
  }
}

// 处理选择变化
function handleSelectionChange(selection: any[]) {
  console.log('选择变化:', selection)
}


// 业务操作函数
function addProduction() {
  dialogMode.value = 'add'
  form.value = {
    id: 0,
    orderNo: generateOrderNo(),
    product: '',
    quantity: '',
    planDate: '',
    remark: ''
  }
  dialogVisible.value = true
}

function editProduction(row: ProductionItem) {
  dialogMode.value = 'edit'
  form.value = {
    id: row.id,
    orderNo: row.orderNo,
    product: row.product,
    quantity: row.quantity,
    planDate: row.planDate,
    remark: ''
  }
  dialogVisible.value = true
}

// 生成订单号
function generateOrderNo(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `PROD-${year}${month}${day}-${random}`
}

// 保存生产单
function saveProduction() {
  if (!form.value.product || !form.value.quantity || !form.value.planDate) {
    console.warn('请填写完整信息')
    return
  }

  const quantity = typeof form.value.quantity === 'string'
    ? parseInt(form.value.quantity)
    : form.value.quantity

  if (dialogMode.value === 'add') {
    // 新增
    const newItem: ProductionItem = {
      id: Date.now(),
      orderNo: form.value.orderNo,
      product: form.value.product,
      quantity: quantity,
      status: '计划中',
      planDate: form.value.planDate,
      actualDate: '',
      progress: 0
    }
    productionList.value.unshift(newItem)
  } else {
    // 编辑
    const index = productionList.value.findIndex(item => item.id === form.value.id)
    if (index > -1) {
      productionList.value[index] = {
        ...productionList.value[index],
        product: form.value.product,
        quantity: quantity,
        planDate: form.value.planDate
      }
    }
  }

  dialogVisible.value = false
}

function startExecution(row: ProductionItem) {
  // 开始执行
  row.status = '执行中'
  row.actualDate = new Date().toISOString().split('T')[0]
  row.progress = 1
  console.log('开始执行:', row)
}

function completeProduction(row: ProductionItem) {
  // 完成生产
  row.status = '已完成'
  row.progress = 100
  console.log('完成生产:', row)
}

function pauseProduction(row: ProductionItem) {
  // 暂停生产
  row.status = '已暂停'
  console.log('暂停生产:', row)
}

function resumeProduction(row: ProductionItem) {
  // 恢复生产
  row.status = '执行中'
  console.log('恢复生产:', row)
}

function searchItems() {
  // 搜索逻辑已通过计算属性实现
  console.log('搜索条件:', search.value)
}

function resetSearch() {
  search.value.keyword = ''
  search.value.status = ''
}
</script>

<style scoped>
/* Production页面特有样式 */
</style>