/* CSS变量定义 - 支持明暗模式切换 */
:root {
  /* 浅色模式 - 现代化玻璃拟态风格 */
  --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --bg-secondary: rgba(248, 250, 252, 0.8);
  --bg-tertiary: rgba(241, 245, 249, 0.6);
  --bg-card: rgba(255, 255, 255, 0.7);
  --bg-sidebar: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  --bg-header: #ffffff;
  
  --text-primary: #1a1a2e;
  --text-secondary: #2d2d44;
  --text-tertiary: #4a4a5a;
  
  --border-color: rgba(226, 232, 240, 0.3);
  --border-hover: rgba(148, 163, 184, 0.4);
  
  --accent-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --accent-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --accent-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --accent-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --accent-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --accent-primary-alpha: rgba(99, 102, 241, 0.15);
  
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --glass-bg: rgba(255, 255, 255, 0.4);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* 新增：发光效果 */
  --glow-primary: 0 0 20px rgba(99, 102, 241, 0.3);
  --glow-secondary: 0 0 20px rgba(6, 182, 212, 0.3);
  --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);

  /* 主色调与辅助色 */
  --erp-primary: #2563eb;
  --erp-success: #10b981;
  --erp-warning: #f59e42;
  --erp-danger: #ef4444;
  --erp-bg: #f1f5f9;
  --erp-sidebar: #1e293b;
  --erp-sidebar-active: #334155;
  --erp-border-radius: 12px;
  --erp-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
}

[data-theme="dark"] {
  /* 深色模式变量 - 现代化玻璃拟态风格 */
  --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --bg-secondary: rgba(30, 41, 59, 0.8);
  --bg-tertiary: rgba(51, 65, 85, 0.6);
  --bg-card: rgba(30, 41, 59, 0.7);
  --bg-sidebar: linear-gradient(145deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  --bg-header: #1e293b;
  
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  
  --border-color: rgba(51, 65, 85, 0.3);
  --border-hover: rgba(100, 116, 139, 0.4);
  
  --accent-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --accent-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --accent-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --accent-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --accent-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --accent-primary-alpha: rgba(99, 102, 241, 0.2);
  
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.7), 0 10px 10px -5px rgba(0, 0, 0, 0.6);
  
  --glass-bg: rgba(30, 41, 59, 0.4);
  --glass-border: rgba(51, 65, 85, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
  
  /* 新增：发光效果 */
  --glow-primary: 0 0 25px rgba(99, 102, 241, 0.4);
  --glow-secondary: 0 0 25px rgba(6, 182, 212, 0.4);
  --glow-success: 0 0 25px rgba(16, 185, 129, 0.4);
}

/* 全局样式 - 现代化玻璃拟态风格 */
body {
  background: var(--erp-bg);
  color: #1a1a2e;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  margin: 0;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.6;
  font-weight: 500;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 简化的全局背景 */

/* 顶部导航/侧边栏 */
.el-menu {
  background: var(--bg-sidebar) !important;
  color: var(--text-primary) !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-lg);
  border: none !important;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}
.el-menu .el-menu-item {
  border-radius: 12px;
  margin: 4px 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-secondary) !important;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}
/* 移除多余的背景动画效果 */
.el-menu .el-menu-item.is-active {
  background: var(--accent-primary) !important;
  color: #ffffff !important;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}
.el-menu .el-menu-item:hover {
  background: var(--glass-bg) !important;
  color: var(--text-primary) !important;
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}


.el-card__body {
  color: var(--text-secondary) !important;
}

/* 对话框中的主要按钮 */
.el-dialog .el-button--primary {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.2);
  border-color: #4338ca;
}

.el-dialog .el-button--primary:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

/* 对话框中的次要按钮 */
.el-dialog .el-button:not(.el-button--primary) {
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  border: 1px solid #e5e7eb;
}

.el-dialog .el-button:not(.el-button--primary):hover {
  background: rgba(229, 231, 235, 0.9);
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.el-tabs__nav {
  border-radius: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-bottom: 1.5px solid var(--border-color) !important;
  min-height: unset;
  margin-bottom: 0;
  padding: 0 2px;
  display: flex;
  align-items: center;
}
.el-tabs__item {
  color: var(--text-secondary) !important;
  transition: all 0.3s ease;
}
.el-tabs__item.is-active {
  color: var(--accent-primary) !important;
}
.el-tabs__active-bar {
  background: var(--accent-primary) !important;
}

/* 删除表格内置滚动条 - 第一组 */

/* 按钮 - 高级现代风格 */
.el-button {
  border-radius: 4px;
  font-weight: 500;
  border: 1px solid transparent;
  padding: 6px 14px;
  font-size: 0.9rem;
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  height: auto;
  line-height: 1.5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  margin: 0;
  white-space: nowrap;
}

.el-button:hover {
  background: rgba(229, 231, 235, 0.9);
  color: #111827;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}
/* 主要按钮 */
.el-button--primary {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-color: #4338ca;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

/* 成功按钮 */
.el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #047857;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}

/* 危险按钮 */
.el-button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-color: #b91c1c;
}

.el-button--danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

/* 警告按钮 */
.el-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border-color: #b45309;
}

.el-button--warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  box-shadow: 0 4px 8px rgba(217, 119, 6, 0.3);
}

/* 信息按钮 */
.el-button--info {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  border-color: #374151;
}

.el-button--info:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  box-shadow: 0 4px 8px rgba(75, 85, 99, 0.3);
}

/* 禁用状态 */
.el-button.is-disabled, 
.el-button:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
  opacity: 0.6;
  border-color: #e5e7eb;
}

/* 小尺寸按钮 */
.el-button--small {
  padding: 4px 10px;
  font-size: 0.8rem;
  border-radius: 3px;
  height: 28px;
}

/* 大尺寸按钮 */
.el-button--large {
  padding: 8px 18px;
  font-size: 1rem;
  border-radius: 5px;
  height: 40px;
}

/* 圆形按钮 */
.el-button.is-circle {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 文本按钮 */
.el-button.is-text {
  background: transparent;
  padding: 4px 8px;
  box-shadow: none;
  color: #6366f1;
  border: none;
}

.el-button.is-text:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #4338ca;
  box-shadow: none;
}

/* === 表单label自适应宽度且右对齐，输入框自适应填满 === */
.el-form-item {
  display: flex !important;
  align-items: center !important;
}
.el-form-item__label {
  display: flex !important;
  align-items: center !important;
  flex: 0 1 auto !important;
  text-align: right !important;
  margin-right: 12px !important;
  width: auto !important;
  min-width: 0 !important;
  max-width: 60%;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
.el-form-item__content {
  flex: 1 1 0% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* 统一输入类控件主风格，删除/注释重复和冲突定义 */
.el-input__wrapper,
.el-select .el-input__wrapper,
.el-date-editor.el-input__wrapper {
  background: var(--bg-card) !important;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  min-height: 36px;
}
.el-input__wrapper:hover,
.el-select .el-input__wrapper:hover,
.el-date-editor.el-input__wrapper:hover {
  border-color: var(--accent-primary) !important;
  box-shadow: var(--shadow-md);
}
.el-input__wrapper:focus-within,
.el-select .el-input__wrapper:focus-within,
.el-date-editor.el-input__wrapper:focus-within {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.12), var(--shadow-md) !important;
  outline: 2px solid var(--accent-primary) !important;
  outline-offset: 0;
}
.el-input__wrapper[disabled],
.el-select .el-input__wrapper[disabled],
.el-date-editor.el-input__wrapper[disabled] {
  background: var(--bg-tertiary) !important;
  color: var(--text-tertiary) !important;
  border-color: var(--border-color) !important;
  opacity: 0.7;
  cursor: not-allowed;
}

/* 选择器 */
.el-select-dropdown {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-xl) !important;
  backdrop-filter: blur(20px);
}
.el-select-dropdown__item {
  color: var(--text-primary) !important;
  transition: all 0.3s ease;
}
.el-select-dropdown__item:hover {
  background: var(--glass-bg) !important;
  color: var(--accent-primary) !important;
}

/* 标签 */
.el-tag {
  border-radius: 8px !important;
  font-weight: 500;
  letter-spacing: 0.3px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.el-tag--success {
  background: var(--accent-success) !important;
  border: none !important;
  color: #ffffff !important;
}
.el-tag--warning {
  background: var(--accent-warning) !important;
  border: none !important;
  color: #ffffff !important;
}
.el-tag--danger {
  background: var(--accent-danger) !important;
  border: none !important;
  color: #ffffff !important;
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--bg-card);
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}
.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}
.theme-toggle svg {
  width: 24px;
  height: 24px;
  color: var(--accent-primary);
  transition: all 0.3s ease;
}
.theme-toggle:hover svg {
  transform: rotate(180deg);
}

/* 步骤条 */
.el-steps {
  background: transparent !important;
}
.el-step__title {
  color: var(--text-primary) !important;
  font-weight: 600;
}
.el-step__description {
  color: var(--text-secondary) !important;
}
.el-step__icon {
  border-color: var(--border-color) !important;
}
.el-step__icon.is-text {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
}
.el-step.is-process .el-step__icon {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
}
.el-step.is-finish .el-step__icon {
  background: var(--accent-success) !important;
  border-color: var(--accent-success) !important;
}

/* 现代化组件样式增强 */
* {
  box-sizing: border-box;
}

/* 简洁的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 现代化按钮样式 */
.btn {
  padding: 14px 28px;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

/* 移除按钮多余背景动画 */

.btn-primary {
  background: var(--accent-primary);
  color: white;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--glass-border);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(-1px) scale(0.98);
}

/* 现代化卡片样式 */
.card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 32px;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(16px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 移除卡片多余背景层 */

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* 输入框样式增强 */
.input {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 16px;
  padding: 16px 20px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.input::placeholder {
  color: var(--text-tertiary);
  transition: color 0.3s ease;
}

/* 选择框样式增强 */
.select {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 16px;
  padding: 16px 20px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  cursor: pointer;
}

.select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: var(--shadow-md);
  }

 

   /* 微交互动画效果 */
   @keyframes pulse {
     0% { transform: scale(1); }
     50% { transform: scale(1.05); }
     100% { transform: scale(1); }
   }

   @keyframes fadeInUp {
     0% {
       opacity: 0;
       transform: translateY(30px);
     }
     100% {
       opacity: 1;
       transform: translateY(0);
     }
   }

   @keyframes slideInRight {
     0% {
       opacity: 0;
       transform: translateX(30px);
     }
     100% {
       opacity: 1;
       transform: translateX(0);
     }
   }

   @keyframes shimmer {
     0% {
       background-position: -200px 0;
     }
     100% {
       background-position: calc(200px + 100%) 0;
     }
   }

   /* 页面加载动画 */
   .fade-enter-active,
   .fade-leave-active {
     transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
   }

   .fade-enter-from {
     opacity: 0;
     transform: translateY(20px);
   }

   .fade-leave-to {
     opacity: 0;
     transform: translateY(-20px);
   }

   /* 悬浮效果增强 */
   .hover-lift {
     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   }

   .hover-lift:hover {
     transform: translateY(-4px);
     box-shadow: var(--shadow-lg);
   }

   /* 点击波纹效果 */
   .ripple {
     position: relative;
     overflow: hidden;
   }

   .ripple::before {
     content: '';
     position: absolute;
     top: 50%;
     left: 50%;
     width: 0;
     height: 0;
     border-radius: 50%;
     background: rgba(255, 255, 255, 0.3);
     transform: translate(-50%, -50%);
     transition: width 0.6s, height 0.6s;
   }

   .ripple:active::before {
     width: 300px;
     height: 300px;
   }

   /* 加载骨架屏效果 */
   .skeleton {
     background: linear-gradient(90deg, 
       var(--glass-bg) 25%, 
       rgba(255, 255, 255, 0.1) 50%, 
       var(--glass-bg) 75%);
     background-size: 200px 100%;
     animation: shimmer 1.5s infinite;
   }

   /* 状态指示器 */
   .status-indicator {
     display: inline-block;
     width: 8px;
     height: 8px;
     border-radius: 50%;
     margin-right: 8px;
     animation: pulse 2s infinite;
   }

   .status-indicator.success {
     background: var(--accent-success);
     box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
   }

   .status-indicator.warning {
     background: var(--accent-warning);
     box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
   }

   .status-indicator.danger {
     background: var(--accent-danger);
     box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
   }

   /* 响应式设计增强 */
   @media (max-width: 768px) {
     .login-card {
       margin: 20px;
       padding: 32px 24px !important;
       border-radius: 24px !important;
     }

     .side-menu {
       width: 100% !important;
       height: auto !important;
       position: relative !important;
     }

     .main-header {
       padding: 0 16px !important;
       font-size: 1.1rem !important;
     }

     .user-info {
       padding: 16px !important;
       margin-bottom: 24px !important;
     }

     .card {
       padding: 20px !important;
       border-radius: 16px !important;
     }
   }

   @media (max-width: 480px) {
     .login-card {
       margin: 10px;
       padding: 24px 16px !important;
       border-radius: 20px !important;
     }

     .btn {
       padding: 12px 20px !important;
       font-size: 0.9rem !important;
     }

     .stat-value {
       font-size: 1.5rem !important;
     }
   }

 /* 内容区留白 */
.el-main, .el-container {
  padding: 24px 32px !important;
  transition: all 0.3s ease;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
  font-weight: 700;
  letter-spacing: 0.3px;
  margin-bottom: 16px;
  transition: color 0.3s ease;
}
h2 {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.8rem;
}

/* 分割线 */
.el-divider {
  border-color: var(--border-color) !important;
}

/* 头像 */
.el-avatar {
  box-shadow: var(--shadow-md) !important;
  border: 2px solid var(--border-color) !important;
  transition: all 0.3s ease;
}
.el-avatar:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: scale(1.05);
}

/* 加载动画 */
.el-loading-mask {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(10px);
}

/* 消息提示 */
.el-message {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-lg) !important;
  backdrop-filter: blur(20px);
}
.el-message__content {
  color: var(--text-primary) !important;
}

/* 通知 */
.el-notification {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-xl) !important;
  backdrop-filter: blur(20px);
}
.el-notification__title {
  color: var(--text-primary) !important;
}
.el-notification__content {
  color: var(--text-secondary) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-main, .el-container {
    padding: 16px 20px !important;
  }
  .theme-toggle {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
  }
  .theme-toggle svg {
    width: 20px;
    height: 20px;
  }
}

/* 表格操作列按钮彻底垂直居中，按钮高度与行一致 */
.el-table .el-table__cell {
  vertical-align: middle !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 48px !important;
}
.el-table .el-table__cell .el-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  min-width: 56px;
  margin: 0 3px;
  padding: 0 10px;
  font-size: 0.8rem;
  border-radius: 3px;
  line-height: 1;
  box-shadow: none;
}

.el-table .el-table__cell .el-button + .el-button {
  margin-left: 5px;
}

/* 表格中的按钮悬浮效果更柔和 */
.el-table .el-table__cell .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 表格中的主要按钮更紧凑 */
.el-table .el-table__cell .el-button--primary,
.el-table .el-table__cell .el-button--success,
.el-table .el-table__cell .el-button--danger,
.el-table .el-table__cell .el-button--warning,
.el-table .el-table__cell .el-button--info {
  background: none;
  border: 1px solid;
}

.el-table .el-table__cell .el-button--primary {
  color: #4f46e5;
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.05);
}

.el-table .el-table__cell .el-button--primary:hover {
  color: white;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.el-table .el-table__cell .el-button--success {
  color: #059669;
  border-color: #059669;
  background: rgba(5, 150, 105, 0.05);
}

.el-table .el-table__cell .el-button--success:hover {
  color: white;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.el-table .el-table__cell .el-button--danger {
  color: #dc2626;
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.el-table .el-table__cell .el-button--danger:hover {
  color: white;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* === 极简直角风格Tabs覆盖 === */
.el-tabs__item,
.el-tabs__item.is-active,
.el-tabs__item:focus,
.el-tabs__item:hover {
  border-radius: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  transition: color 0.2s;
}
.el-tabs__item.is-active {
  color: var(--accent-primary) !important;
  border-bottom: 2px solid var(--accent-primary) !important;
  background: transparent !important;
  font-weight: 600 !important;
}
.el-tabs__active-bar {
  border-radius: 0 !important;
  height: 2px !important;
}

/* === 极简大气小卡片/弹窗风格覆盖 === */
.el-popover, .el-message-box, .el-popconfirm {
  border-radius: 8px !important;
  background: #fff !important;
  border: none !important;
  box-shadow: 0 6px 32px 0 rgba(37,99,235,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.04) !important;
  color: #1a1a2e !important;
  padding: 18px 24px !important;
  min-width: 220px;
  max-width: 360px;
  font-size: 1rem !important;
  font-weight: 500;
  backdrop-filter: none !important;
}
.el-popover__title, .el-message-box__title, .el-popconfirm__main {
  color: var(--accent-primary) !important;
  font-weight: 600;
  font-size: 1.08rem;
  margin-bottom: 8px;
  background: transparent !important;
}
.el-message-box__btns, .el-popconfirm__action {
  background: transparent !important;
  border-top: none !important;
  padding: 0 0 0 0 !important;
  margin-top: 18px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
.el-message-box__btns .el-button, .el-popconfirm__action .el-button {
  min-width: 70px;
  font-size: 0.9rem;
  padding: 6px 14px;
  height: 34px;
}

/* 消息框和确认框中的主要按钮 */
.el-message-box__btns .el-button--primary, .el-popconfirm__action .el-button--primary {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.2);
  border-color: #4338ca;
}

/* 消息框和确认框中的次要按钮 */
.el-message-box__btns .el-button:not(.el-button--primary), .el-popconfirm__action .el-button:not(.el-button--primary) {
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  border: 1px solid #e5e7eb;
}

/* === 极简大气 el-dialog 关闭按钮样式覆盖 === */
.el-dialog__headerbtn {
  top: 18px !important;
  right: 18px !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background: none !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  transition: background 0.18s;
}
.el-dialog__headerbtn .el-dialog__close {
  font-size: 20px !important;
  color: #2563eb !important;
  opacity: 1 !important;
  font-weight: 700;
  transition: color 0.18s;
}
.el-dialog__headerbtn:hover {
  background: #e8f0fe !important;
}
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #1d4ed8 !important;
}

/* === 全局响应式增强 === */
@media (max-width: 1024px) {
  .erp-stats-row {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
  }
  
  .erp-table-toolbar > .el-input,
  .erp-table-toolbar > .el-select,
  .erp-table-toolbar > .el-date-picker {
    min-width: 140px !important;
    max-width: 180px !important;
    flex: 0 0 auto !important;
    margin: 0 4px 0 0 !important;
  }
  .erp-table-toolbar > .el-button {
    min-width: 80px !important;
    flex: 0 0 auto !important;
    margin: 0 4px !important;
  }
}
@media (max-width: 768px) {
  .erp-dashboard-page, .erp-card {
    padding: 8px !important;
  }
  .erp-stats-row {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
  /* 删除工具栏滚动条 - 第一处 */
  .erp-table, .el-table {
    font-size: 0.92rem !important;
  }
  .el-input, .el-select, .el-date-picker {
    box-sizing: border-box !important;
  }
  
  .el-button {
    min-width: 0;
    box-sizing: border-box;
  }
  .erp-table-toolbar > .el-input,
  .erp-table-toolbar > .el-select,
  .erp-table-toolbar > .el-date-picker {
    min-width: 120px !important;
    max-width: 160px !important;
    flex: 0 0 auto !important;
    margin: 0 4px 0 0 !important;
  }
  
  .erp-table-toolbar > .el-button {
    min-width: 70px !important;
    flex: 0 0 auto !important;
    margin: 0 3px !important;
    padding: 0 12px !important;
  }
}
@media (max-width: 480px) {
  .erp-dashboard-page, .erp-card {
    padding: 2px !important;
  }
  .erp-stats-row {
    grid-template-columns: 1fr !important;
    gap: 4px !important;
  }
  /* 删除工具栏滚动条 - 第二处 */
  .el-input, .el-select, .el-date-picker {
    font-size: 0.92rem !important;
  }
  .el-button {
    font-size: 0.92rem !important;
  }
  .erp-table-toolbar > .el-input,
  .erp-table-toolbar > .el-select,
  .erp-table-toolbar > .el-date-picker {
    min-width: 100px !important;
    max-width: 140px !important;
    flex: 0 0 auto !important;
    margin: 0 3px 0 0 !important;
    height: 34px !important;
  }
  .erp-table-toolbar > .el-button {
    min-width: 60px !important;
    flex: 0 0 auto !important;
    margin: 0 2px !important;
    height: 34px !important;
    padding: 0 10px !important;
  }
}

/* === 统计卡片自适应宽度与溢出优化 === */
.erp-stat-card {
  min-width: 0;
  word-break: break-all;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: var(--bg-card, #fff);
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
  padding: 16px 14px;
  position: relative;
  min-height: 80px;
  transition: box-shadow 0.18s;
}
.erp-stat-label, .erp-stat-value {
  display: block;
  width: 100%;
  min-width: 0;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
}
@media (max-width: 1024px) {
  .erp-stats-row {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)) !important;
    gap: 10px !important;
  }
}
@media (max-width: 768px) {
  .erp-stats-row {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)) !important;
    gap: 8px !important;
  }
}
@media (max-width: 480px) {
  .erp-stats-row {
    grid-template-columns: 1fr !important;
    gap: 4px !important;
  }
  .erp-stat-card {
    padding: 10px 6px;
  }
}

/* === 全局按钮自适应宽度优化 === */
.btn, .erp-header-btn {
  min-width: 0;
  width: auto;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  box-sizing: border-box;
  border: none !important;
}

.el-button {
  min-width: 0;
  width: auto;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  box-sizing: border-box;
}

/* === 修正弹窗宽度为内容自适应且不全屏 === */
.el-dialog {
  max-width: 96vw !important;
  min-width: min(400px, 96vw) !important;
  width: max-content !important;
  box-sizing: border-box !important;
}

/* === 弹窗字体样式优化 - 加粗显示 === */
.el-dialog__header .el-dialog__title {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog__body {
  color: #1a1a2e !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog__body .el-form-item__label {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.95rem !important;
}

.el-dialog__body .el-input__inner,
.el-dialog__body .el-textarea__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

.el-dialog__body .el-input__inner::placeholder,
.el-dialog__body .el-textarea__inner::placeholder {
  color: #6b7280 !important;
  font-weight: 600 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* === 弹窗下拉选择器字体样式 === */
.el-dialog__body .el-select .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

.el-dialog__body .el-select-dropdown .el-select-dropdown__item {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* === 弹窗按钮字体样式 === */
.el-dialog__footer .btn-blue,
.el-dialog__footer .btn-gray,
.el-dialog__footer button {
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* === 弹窗内日期选择器字体样式 === */
.el-dialog__body .el-date-editor .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* === 弹窗整体字体优化 === */
.el-dialog {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog .el-form {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}



/* === 弹窗内表单项间距和字体调整 === */
.el-dialog__body .el-form-item {
  margin-bottom: 20px !important;
}

.el-dialog__body .el-form-item__content {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* === 弹窗内数字输入框字体样式 === */
.el-dialog__body .el-input[type="number"] .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* === 强制弹窗内所有文字加粗（排除图标） === */
.el-dialog *:not(i) {
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog input,
.el-dialog textarea,
.el-dialog .el-input__inner,
.el-dialog .el-textarea__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog .el-select-dropdown__item {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* === 表格字体颜色优化 === */
.el-table {
  color: #1a1a2e !important;
  font-weight: 500 !important;
}

.el-table th {
  color: #1a1a2e !important;
  font-weight: 600 !important;
}

.el-table td {
  color: #2d2d44 !important;
  font-weight: 500 !important;
}

/* === 统计卡片字体颜色优化 === */
.erp-stat-card .erp-stat-label {
  color: #1a1a2e !important;
  font-weight: 500 !important;
}

.erp-stat-card .erp-stat-value {
  color: #1a1a2e !important;
  font-weight: 700 !important;
}

/* === 搜索栏字体颜色优化 === */
.el-input__inner {
  color: #2d2d44 !important;
  font-weight: 500 !important;
}

.el-input__inner::placeholder {
  color: #6b7280 !important;
  font-weight: 400 !important;
}

.el-select .el-input__inner {
  color: #2d2d44 !important;
  font-weight: 500 !important;
}

/* === 按钮文字颜色优化 === */
.btn-blue, .btn-green, .btn-yellow, .btn-red, .btn-orange {
  font-weight: 600 !important;
}

.btn-blue-light, .btn-green-light, .btn-yellow-light, .btn-red-light, .btn-orange-light {
  font-weight: 600 !important;
}

.btn-gray {
  color: #1a1a2e !important;
  font-weight: 600 !important;
}

/* === 标签页字体颜色优化 === */
.el-tabs__item {
  color: #2d2d44 !important;
  font-weight: 500 !important;
}

.el-tabs__item.is-active {
  color: #1a1a2e !important;
  font-weight: 600 !important;
}
@media (max-width: 1024px) {
  .el-dialog {
    min-width: 0 !important;
    max-width: 98vw !important;
    width: 98vw !important;
    padding: 0 4px !important;
  }
  .el-dialog__body .el-form-item__label {
    flex: 0 0 90px !important;
    width: 90px !important;
    min-width: 60px !important;
    max-width: 120px !important;
    font-size: 0.98rem !important;
  }
}
@media (max-width: 768px) {
  .el-dialog {
    min-width: 0 !important;
    max-width: 100vw !important;
    width: 100vw !important;
    padding: 0 2px !important;
  }
  .el-dialog__body .el-form-item {
    flex-direction: column !important;
    align-items: stretch !important;
    margin-bottom: 12px !important;
  }
  .el-dialog__body .el-form-item__label {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    text-align: left !important;
    margin-bottom: 4px !important;
    font-size: 0.98rem !important;
  }
  .el-dialog__body .el-form-item__content {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }
  .el-dialog__footer {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
    padding: 0 8px 12px 8px !important;
  }
}
@media (max-width: 480px) {
  .el-dialog {
    min-width: 0 !important;
    max-width: 100vw !important;
    width: 100vw !important;
    padding: 0 1px !important;
  }
  .el-dialog__body .el-form-item {
    flex-direction: column !important;
    align-items: stretch !important;
    margin-bottom: 8px !important;
  }
  .el-dialog__body .el-form-item__label {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    text-align: left !important;
    margin-bottom: 2px !important;
    font-size: 0.95rem !important;
  }
  .el-dialog__body .el-form-item__content {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }
  .el-dialog__footer {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 6px !important;
    padding: 0 4px 8px 4px !important;
  }
}

/* === 工具栏按钮区自适应排列 === */
/* 删除工具栏滚动条 */

/* === 表格列内容溢出省略号 === */
/* 移除全局单元格宽度限制，让Element Plus自动分配 */

/* === 强化el-dialog__body下表单对齐，彻底修复弹窗表单label和输入框对齐 === */
.el-dialog__body .el-form {
  display: flex !important;
  flex-direction: column !important;
  gap: 0 !important;
}
.el-dialog__body .el-form-item {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  margin-bottom: 18px !important;
}
.el-dialog__body .el-form-item__label {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-variant-ligatures: none !important;
  font-size: 1rem !important;
  flex: 0 0 120px !important;
  width: 120px !important;
  min-width: 80px !important;
  max-width: 180px !important;
  text-align: left !important;
  white-space: pre !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding-left: 0 !important;
  margin-right: 12px !important;
}
.el-dialog__body .el-form-item__content {
  flex: 1 1 0% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  display: flex !important;
  align-items: center !important;
}
.el-dialog__footer {
  display: flex !important;
  justify-content: center !important;
  gap: 18px !important;
  padding-bottom: 24px !important;
}

.erp-table-toolbar > .el-input,
.erp-table-toolbar > .el-select,
.erp-table-toolbar > .el-date-picker {
  height: 36px;
  min-height: 36px;
  align-items: center;
  display: flex;
  flex: 0 0 auto;
  margin: 0 8px 0 0;
  white-space: nowrap;
}

/* 确保输入框内部元素正确对齐 */
.erp-table-toolbar > .el-input .el-input__wrapper,
.erp-table-toolbar > .el-select .el-input__wrapper,
.erp-table-toolbar > .el-date-picker .el-input__wrapper {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  display: flex;
  align-items: center;
}

.erp-table-toolbar > .el-button {
  height: 36px;
  min-height: 36px;
  align-items: center;
  justify-content: center;
  display: flex;
  box-sizing: border-box;
  min-width: 80px;
  flex: 0 0 auto;
  padding: 0 16px;
  margin: 0 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 工具栏中的按钮图标对齐 */
.erp-table-toolbar > .el-button > i,
.erp-table-toolbar > .el-button > .el-icon {
  margin-right: 4px;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 表格基础样式已在后面统一定义 */

/* 删除表格内置滚动条 - 第二组 */

/* 强制表格适应容器宽度 */
.el-table,
.el-table__header-wrapper,
.el-table__body-wrapper,
.el-table__footer-wrapper {
  width: 100% !important;
}

/* 修复表格列宽计算问题 */
.el-table colgroup col {
  width: auto !important;
}

/* 强制表格单元格样式生效 */
.el-table .el-table__cell {
  box-sizing: border-box !important;
}

/* 修复固定列样式 */
.el-table .el-table__fixed,
.el-table .el-table__fixed-right {
  box-shadow: var(--shadow-sm) !important;
  z-index: 2 !important;
  background-color: white !important;
  height: 100% !important;
}

/* 强制表格内容在小屏幕上合理显示 */
.el-table__cell {
  min-width: 0 !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  /* 移除 overflow: hidden，让 Element Plus 处理滚动 */
  vertical-align: middle !important;
  padding: 12px 8px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

/* 增强表头样式 */
.el-table__header th.el-table__cell {
  font-weight: 600 !important;
  color: #1f2937 !important;
  background-color: #f9fafb !important;
  border-bottom: 2px solid #e5e7eb !important;
  padding: 12px 8px !important;
}

.el-table__cell .el-button,
.el-table__cell .el-tag {
  max-width: 100%;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

/* 删除表格响应式滚动条 - 第一处 */

/* 使表格更具辨识度 */
.erp-table-container {
  border-radius: 8px;
  overflow: hidden;
  background: white;
  margin-bottom: 20px;
}

/* 删除表格包装器滚动条 - 第四处 */

/* 重设媒体查询，简化表格边距设置 */
@media (max-width: 768px) {
  .el-table,
  .erp-table {
    font-size: 0.92rem !important;
    width: 100% !important;
  }
  
  .el-table__header th.el-table__cell {
    padding: 8px 6px !important;
  }
  
  .el-table__body td.el-table__cell {
    padding: 8px 6px !important;
  }
  
  .cell {
    gap: 6px !important;
  }
}

@media (max-width: 480px) {
  .el-table,
  .erp-table {
    font-size: 0.9rem !important;
    width: 100% !important;
  }
  
  .el-table__header th.el-table__cell {
    padding: 6px 4px !important;
    font-size: 0.85rem !important;
  }
  
  .el-table__body td.el-table__cell {
    padding: 6px 4px !important;
  }
  
  .cell {
    gap: 4px !important;
  }
}

/* 强制Element Plus表格样式覆盖 */
.el-table {
  --el-table-border-color: #e5e7eb !important;
  --el-table-border: 1px solid #e5e7eb !important;
  --el-table-text-color: #374151 !important;
  --el-table-header-text-color: #1f2937 !important;
  --el-table-row-hover-bg-color: #f3f4f6 !important;
  --el-table-header-bg-color: #f9fafb !important;
  --el-table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
  --el-table-bg-color: #ffffff !important;
  --el-table-tr-bg-color: #ffffff !important;
  --el-table-expanded-cell-bg-color: #f9fafb !important;
  border-radius: 8px !important;
  /* 移除 overflow: hidden，让 Element Plus 处理滚动 */
  border: 1px solid #e5e7eb !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-table--border, 
.el-table--group {
  border: 1px solid #e5e7eb !important;
}

.el-table--border::after, 
.el-table--group::after {
  width: 100% !important;
}

.el-select--small .el-select__wrapper {
  min-width: 120px !important;
  max-width: 220px !important;
  width: auto !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
  min-height: 36px !important;
}

/* 确保erp-table-toolbar中的下拉选择器正确显示 */
.erp-table-toolbar .el-select {
  height: 100% !important;
}

.erp-table-toolbar .el-select .el-input {
  width: 100% !important;
}

.erp-table-toolbar .el-select .el-input__wrapper {
  width: 100% !important;
}

/* 确保erp-table-toolbar在所有设备上都能正确显示 */
.erp-table-toolbar::-webkit-scrollbar {
  height: 4px;
}

.erp-table-toolbar::-webkit-scrollbar-track {
  background: transparent;
}

.erp-table-toolbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.erp-table-toolbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 删除触摸滚动设置 */

/* ========================================
   为 pane-production 添加类似Element Plus的自定义滚动条
   ======================================== */

/* 为 el-table__inner-wrapper 添加自定义滚动条 */
.el-table__inner-wrapper {
  position: relative !important;
}

/* 自定义水平滚动条 */
.el-table__inner-wrapper .custom-scrollbar-horizontal {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

/* 水平滚动条滑块 */
.el-table__inner-wrapper .custom-scrollbar-horizontal .custom-scrollbar-thumb {
  position: absolute;
  top: 0;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
  cursor: pointer;
  min-width: 20px;
}

/* hover时显示滚动条 */
.el-table__inner-wrapper:hover .custom-scrollbar-horizontal {
  opacity: 1 !important;
}

/* 滚动条滑块hover效果 */
.el-table__inner-wrapper .custom-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 滚动时显示滚动条 */
.el-table__inner-wrapper.scrolling .custom-scrollbar-horizontal {
  opacity: 1 !important;
}

/* 调试：让滚动条始终可见来测试 */
/* .el-table__inner-wrapper .custom-scrollbar-horizontal {
  opacity: 1 !important;
  background: red !important;
} */

/* 表头和内容滚动同步 */
.erp-data-table .el-table__header-wrapper {
  overflow-x: hidden !important; /* 隐藏表头滚动条，使用transform同步 */
}

.erp-data-table .el-table__body-wrapper {
  overflow-x: auto !important;
}

/* 统一表头和内容的padding */
.erp-data-table .el-table th,
.erp-data-table .el-table td {
  padding: 12px 0px !important; /* 左右边距为0，文本本身有边距 */
}

/* 强制覆盖Element Plus的默认padding */
.erp-data-table .el-table .el-table__cell {
  padding: 12px 0px !important;
}

/* 更具体的选择器确保覆盖 */
.erp-data-table .el-table__body .el-table__row .el-table__cell,
.erp-data-table .el-table__header .el-table__row .el-table__cell {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

/* 现代扁平化按钮 Modern Flat Button Styles - 优先级提升 */
.erp-flat-btn {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  border: none !important;
  border-radius: 0.25rem !important;
  padding: 0.75rem 1.5rem !important;
  box-shadow: 0 2px 8px 0 rgba(30, 42, 80, 0.08) !important;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s !important;
  gap: 0.5rem !important;
  cursor: pointer !important;
  outline: none !important;
  white-space: nowrap !important;
  background: none;
}
.erp-flat-btn i {
  font-size: 1.25em !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-right: 4px !important;
}
.erp-flat-btn span {
  display: inline-block !important;
  line-height: 1 !important;
  white-space: nowrap !important;
}
.erp-flat-btn.blue {
  background: #2563eb !important;
  color: #fff !important;
}
.erp-flat-btn.blue:hover {
  background: #1d4ed8 !important;
}
.erp-flat-btn.green {
  background: #22c55e !important;
  color: #fff !important;
}
.erp-flat-btn.green:hover {
  background: #16a34a !important;
}
.erp-flat-btn.red {
  background: #ef4444 !important;
  color: #fff !important;
}
.erp-flat-btn.red:hover {
  background: #b91c1c !important;
}
.erp-flat-btn.yellow {
  background: #f59e42 !important;
  color: #fff !important;
}
.erp-flat-btn.yellow:hover {
  background: #eab308 !important;
}
.erp-flat-btn.light.blue {
  background: #dbeafe !important;
  color: #2563eb !important;
  border: none !important;
  box-shadow: none !important;
}
.erp-flat-btn.light.blue:hover {
  background: #bfdbfe !important;
  color: #1d4ed8 !important;
}
.erp-flat-btn.light.green {
  background: #dcfce7 !important;
  color: #16a34a !important;
  border: none !important;
  box-shadow: none !important;
}
.erp-flat-btn.light.green:hover {
  background: #bbf7d0 !important;
  color: #15803d !important;
}
.erp-flat-btn.light.red {
  background: #fee2e2 !important;
  color: #dc2626 !important;
  border: none !important;
  box-shadow: none !important;
}
.erp-flat-btn.light.red:hover {
  background: #fecaca !important;
  color: #b91c1c !important;
}
.erp-flat-btn.light.yellow {
  background: #fef9c3 !important;
  color: #ca8a04 !important;
  border: none !important;
  box-shadow: none !important;
}
.erp-flat-btn.light.yellow:hover {
  background: #fef08a !important;
  color: #a16207 !important;
}

/* 删除单元格滚动条 - 第五处 */

/* 增强表格hover效果 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #f3f4f6 !important;
  transition: background 0.18s;
  z-index: 1;
}


/* 表格内的按钮样式优化 */
.el-table .el-button {
  padding: 6px 12px !important;
  font-size: 0.85rem !important;
}

/* 删除表格内置滚动条 - 第三组 */

/* 移除表格特定滚动条样式，使用全局统一样式 */

/* 响应式表格布局增强 */
@media (max-width: 1024px) {
  .el-table,
  .erp-table {
    font-size: 0.95rem !important;
  }
  
  .el-table__header th.el-table__cell {
    padding: 10px 8px !important;
  }
  
  .el-table__body td.el-table__cell {
    padding: 10px 8px !important;
  }
}

@media (max-width: 768px) {
  .el-table,
  .erp-table {
    font-size: 0.92rem !important;
  }
  
  .el-table__header th.el-table__cell {
    padding: 8px 6px !important;
  }
  
  .el-table__body td.el-table__cell {
    padding: 8px 6px !important;
  }
  
  /* 优化表格内按钮在小屏幕上的显示 */
  .cell {
    gap: 6px !important;
  }
  
  .cell .erp-flat-btn {
    padding: 0.25rem 0.5rem !important;
    min-width: 50px !important;
  }
}

@media (max-width: 480px) {
  .el-table,
  .erp-table {
    font-size: 0.9rem !important;
  }
  
  .el-table__header th.el-table__cell {
    padding: 6px 4px !important;
    font-size: 0.85rem !important;
  }
  
  .el-table__body td.el-table__cell {
    padding: 6px 4px !important;
  }
  
  /* 极小屏幕优化表格内按钮 */
  .cell {
    gap: 4px !important;
  }
  
  .cell .erp-flat-btn {
    padding: 0.2rem 0.4rem !important;
    min-width: 45px !important;
  }
  
  .cell .erp-flat-btn i {
    margin-right: 2px !important;
  }
}

/* 增强表格内容溢出控制 */
/* 移除最大宽度限制，让Element Plus自动分配宽度 */

/* 表格分页器样式增强 */
.el-pagination {
  margin-top: 16px !important;
  padding: 0 !important;
  justify-content: flex-end !important;
  font-weight: 500 !important;
}

.el-pagination .el-pagination__total,
.el-pagination .el-pagination__sizes,
.el-pagination .el-pagination__jump {
  margin-right: 16px !important;
}

.el-pagination .el-pager li {
  border-radius: 4px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
}

.el-pagination .el-pager li.is-active {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

/* 增强表格响应式布局 - 特定列宽度控制 */
@media (max-width: 768px) {
  .el-table .el-table__body td.el-table__cell {
    max-width: 150px !important;
  }
}

@media (max-width: 480px) {
  .el-table .el-table__body td.el-table__cell {
    max-width: 120px !important;
  }
}

/* Tailwind风格按钮样式 */
.bg-blue-600 {
  background-color: #2563eb !important;
}
.hover\:bg-blue-700:hover {
  background-color: #1d4ed8 !important;
}
.bg-blue-100 {
  background-color: #dbeafe !important;
}
.hover\:bg-blue-200:hover {
  background-color: #bfdbfe !important;
}
.text-blue-700 {
  color: #1d4ed8 !important;
}

.bg-red-600 {
  background-color: #dc2626 !important;
}
.hover\:bg-red-700:hover {
  background-color: #b91c1c !important;
}
.bg-red-100 {
  background-color: #fee2e2 !important;
}
.hover\:bg-red-200:hover {
  background-color: #fecaca !important;
}
.text-red-700 {
  color: #b91c1c !important;
}

.bg-green-600 {
  background-color: #16a34a !important;
}
.hover\:bg-green-700:hover {
  background-color: #15803d !important;
}
.bg-green-100 {
  background-color: #dcfce7 !important;
}
.hover\:bg-green-200:hover {
  background-color: #bbf7d0 !important;
}
.text-green-700 {
  color: #15803d !important;
}

.bg-yellow-500 {
  background-color: #eab308 !important;
}
.hover\:bg-yellow-600:hover {
  background-color: #ca8a04 !important;
}
.bg-yellow-100 {
  background-color: #fef9c3 !important;
}
.hover\:bg-yellow-200:hover {
  background-color: #fef08a !important;
}
.text-yellow-700 {
  color: #a16207 !important;
}

.bg-purple-100 {
  background-color: #f3e8ff !important;
}
.hover\:bg-purple-200:hover {
  background-color: #e9d5ff !important;
}
.text-purple-700 {
  color: #7e22ce !important;
}

.bg-gray-100 {
  background-color: #f3f4f6 !important;
}
.hover\:bg-gray-200:hover {
  background-color: #e5e7eb !important;
}
.text-gray-700 {
  color: #374151 !important;
}

.text-white {
  color: #ffffff !important;
}

.font-bold {
  font-weight: 700 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}
.px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}
.px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.px-6 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

.rounded-lg {
  border-radius: 0.5rem !important;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}
.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.transition {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform !important;
}
.duration-200 {
  transition-duration: 200ms !important;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.flex {
  display: flex !important;
}
.items-center {
  align-items: center !important;
}
.justify-center {
  justify-content: center !important;
}
.justify-end {
  justify-content: flex-end !important;
}
.space-x-1 > * + * {
  margin-left: 0.25rem !important;
}
.space-x-2 > * + * {
  margin-left: 0.5rem !important;
}
.space-x-3 > * + * {
  margin-left: 0.75rem !important;
}
.space-x-4 > * + * {
  margin-left: 1rem !important;
}
.mt-4 {
  margin-top: 1rem !important;
}
.mb-4 {
  margin-bottom: 1rem !important;
}

/* 其他布局工具类 */
.grid {
  display: grid !important;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
}
.gap-6 {
  gap: 1.5rem !important;
}

@media (min-width: 640px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }
}

/* 移除不必要的表格容器样式，直接使用Element Plus的表格 */

/* 统一表格样式 - 默认使用auto布局 */
.erp-table,
.el-table {
  width: 100% !important;
  table-layout: auto !important; /* 默认使用auto布局，让表格自适应内容 */
  font-size: 0.98rem !important;
  border-radius: 8px !important;
  margin-top: 16px !important;
  margin-bottom: 16px !important;
  border: 1px solid #e5e7eb !important;
}

/* 移除添加的滚动条设置，使用现有的内置滚动条 */

/* 确保表格列宽按比例分配 */
.el-table colgroup col {
  width: auto !important;
}

/* 表格单元格内容优化 */
.el-table .el-table__cell {
  padding: 8px 6px !important;
  vertical-align: middle !important;
  word-break: break-word !important;
  /* 移除 overflow: hidden，让 Element Plus 处理滚动 */
  text-overflow: ellipsis !important;
}

/* 删除表格容器滚动条 */

.el-table__header-wrapper,
.el-table__body-wrapper {
  overflow: visible !important;
}

/* 表格头部样式 */
.el-table__header,
.erp-table .el-table__header {
  background-color: #f9fafb !important;
  width: 100% !important;
  table-layout: auto !important; /* 默认使用auto布局，让表头自适应 */
}

.el-table__header th.el-table__cell,
.erp-table .el-table__header th.el-table__cell {
  background-color: #f9fafb !important;
  border-bottom: 2px solid #e5e7eb !important;
  color: #1f2937 !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
  text-align: left !important;
}

/* 表格单元格样式 - 优化自适应布局 */
.el-table__cell,
.erp-table .el-table__cell {
  padding: 12px 8px !important;
  border-bottom: 1px solid #e5e7eb !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
  text-align: left !important; /* 确保内容左对齐 */
}

/* 表格行hover效果 - 确保包括操作列 */
.el-table__row:hover .el-table__cell {
  background-color: #f5f7fa !important;
}

/* 操作列按钮容器优化 */
.el-table__cell .cell {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
}

/* 表格滚动容器 - 移除固定最小宽度，让表格自适应 */
.el-table {
  min-width: 100% !important; /* 表格宽度自适应容器 */
}

/* Element Plus表格的滚动容器 */
.el-scrollbar,
/* 移除重复的滚动容器定义，使用Element Plus内置滚动 */

/* 表格列自适应宽度 */
.el-table__header-wrapper,
.el-table__body-wrapper {
  overflow: visible !important;
}

/* 操作列按钮优化 */
.el-table__cell .cell,
.el-table__cell .action-buttons {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
  justify-content: flex-start !important;
  white-space: nowrap !important;
  /* 移除固定最小宽度，让容器根据内容自适应 */
}

/* 紧凑型按钮样式 */
.btn-blue-light.compact,
.btn-green-light.compact,
.btn-yellow-light.compact,
.btn-orange-light.compact,
.btn-red-light.compact,
.el-table .btn-blue-light.compact,
.el-table .btn-green-light.compact,
.el-table .btn-yellow-light.compact,
.el-table .btn-orange-light.compact,
.el-table .btn-red-light.compact,
.content-fit-table .btn-blue-light.compact,
.content-fit-table .btn-green-light.compact,
.content-fit-table .btn-yellow-light.compact,
.content-fit-table .btn-orange-light.compact,
.content-fit-table .btn-red-light.compact {
  padding: 5px 10px !important;
  font-size: 0.85rem !important;
  /* 移除固定宽度，让按钮根据内容自适应 */
  height: 30px !important;
  white-space: nowrap !important;
  flex: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
}

.btn-blue-light.compact i,
.btn-green-light.compact i,
.btn-yellow-light.compact i,
.btn-red-light.compact i,
.el-table .btn-blue-light.compact i,
.el-table .btn-green-light.compact i,
.el-table .btn-yellow-light.compact i,
.el-table .btn-red-light.compact i,
.content-fit-table .btn-blue-light.compact i,
.content-fit-table .btn-green-light.compact i,
.content-fit-table .btn-yellow-light.compact i,
.content-fit-table .btn-red-light.compact i {
  font-size: 0.85rem !important;
  margin-right: 0 !important;
  flex-shrink: 0 !important;
}

.btn-blue-light.compact span,
.btn-green-light.compact span,
.btn-yellow-light.compact span,
.btn-red-light.compact span,
.el-table .btn-blue-light.compact span,
.el-table .btn-green-light.compact span,
.el-table .btn-yellow-light.compact span,
.el-table .btn-red-light.compact span,
.content-fit-table .btn-blue-light.compact span,
.content-fit-table .btn-green-light.compact span,
.content-fit-table .btn-yellow-light.compact span,
.content-fit-table .btn-red-light.compact span {
  flex-shrink: 0 !important;
}

/* content-fit-table: 使用Element Plus内置滚动 + 智能列宽 */
.content-fit-table.el-table {
  table-layout: fixed !important; /* 使用fixed确保列宽一致 */
  width: 100% !important; /* 充满容器宽度 */
  min-width: 100% !important; /* 最小宽度确保充满 */
}

.content-fit-table .el-table__header {
  table-layout: fixed !important; /* 表头使用fixed布局 */
  width: 100% !important; /* 表头充满宽度 */
}

.content-fit-table .el-table__body {
  table-layout: fixed !important; /* 表体使用fixed布局 */
  width: 100% !important; /* 表体充满宽度 */
}

/* 让列宽基于内容自适应 */
.content-fit-table .el-table__header th,
.content-fit-table .el-table__body td {
  width: auto !important;
  white-space: nowrap !important; /* 防止文本换行，确保内容决定宽度 */
}

/* 智能列宽分配：压缩短内容列，扩展长内容列 */
.content-fit-table colgroup col {
  width: 1% !important; /* 让所有列先收缩到最小 */
  min-width: 0 !important; /* 允许列收缩 */
}

/* colgroup充满宽度 */
.content-fit-table colgroup {
  width: 100% !important; /* 充满容器宽度 */
}

/* 表格自动调整：内容决定最小宽度，剩余空间平均分配 */
.content-fit-table .el-table__header-wrapper,
.content-fit-table .el-table__body-wrapper {
  width: 100% !important;
  min-width: 100% !important; /* 确保至少填满容器 */
}

/* 表格主体确保填满容器 */
.content-fit-table .el-table__header,
.content-fit-table .el-table__body {
  width: 100% !important;
  min-width: 100% !important;
}

/* 表格单元格样式：让Element Plus自动分配宽度 */
.content-fit-table .el-table__cell {
  padding: 8px 12px 8px 8px !important;
  white-space: nowrap !important;
  box-sizing: border-box !important;
  /* 移除所有宽度和overflow限制，让Element Plus自动处理 */
}

/* 操作列特殊处理：不被压缩，确保按钮完整显示 */
.content-fit-table .el-table__cell:last-child {
  max-width: none !important; /* 操作列不被压缩 */
  overflow: visible !important; /* 按钮不被截断 */
  white-space: nowrap !important;
  width: auto !important; /* 根据按钮内容确定宽度 */
}

/* 操作列的按钮容器 */
.content-fit-table .el-table__cell .action-buttons {
  overflow: visible !important;
  white-space: nowrap !important;
  display: flex !important;
  gap: 6px !important;
  align-items: center !important;
  min-width: max-content !important; /* 确保按钮容器不被压缩 */
}

/* 允许现有滚动条正常工作，只设置宽度 */
.el-scrollbar__view {
  width: 100% !important;
}

.el-scrollbar {
  width: 100% !important;
}

.el-scrollbar__wrap {
  width: 100% !important;
}

.el-table__inner-wrapper {
  width: 100% !important;
}

/* 表格容器正常显示 */
.content-fit-table {
  width: 100% !important;
}

/* 表格容器：确保充满宽度，让Element Plus处理滚动 */
.content-fit-table .el-table__header-wrapper,
.content-fit-table .el-table__body-wrapper {
  width: 100% !important; /* 充满容器宽度 */
  min-width: 100% !important; /* 最小宽度确保充满 */
}

.content-fit-table .el-table__header,
.content-fit-table .el-table__body {
  width: 100% !important; /* 充满容器宽度 */
  min-width: 100% !important; /* 最小宽度确保充满 */
}

/* 删除表格外层滚动条样式 */

/* CSS强制不换行 + 自动宽度策略完成 */

/* 表格头部单元格 - 恢复左边距 */
.content-fit-table .el-table__header .el-table__cell {
  padding: 12px 12px 12px 8px !important; /* 恢复左边距 */
  font-weight: 600 !important;
  width: auto !important;
}

/* 删除表格滚动容器 - 第六处 */

/* 移除重复定义，使用上面的统一定义 */

/* 操作按钮容器优化 */
.content-fit-table .action-buttons {
  display: flex !important;
  gap: 6px !important;
  align-items: center !important;
  justify-content: flex-start !important;
  white-space: nowrap !important;
  width: auto !important; /* 自适应宽度 */
  box-sizing: border-box !important;
  overflow: visible !important;
}

/* 移除操作列特殊处理，所有列统一样式 */

/* 数字列内容右对齐 */
.content-fit-table .el-table__body .el-table__cell:nth-child(4),
.content-fit-table .el-table__body .el-table__cell:nth-child(5),
.content-fit-table .el-table__body .el-table__cell:nth-child(6) {
  text-align: right !important;
}

/* 表格内容区域样式 */
.content-fit-table .el-table__cell .cell {
  /* 移除overflow限制，让Element Plus自动处理 */
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 移除自定义表格滚动容器样式 */

/* 响应式表格优化 */
@media (max-width: 1024px) {
  .el-table .el-table__cell {
    padding: 6px 4px !important;
    font-size: 0.9rem !important;
  }

  .btn-blue-light.compact,
  .btn-green-light.compact {
    padding: 3px 6px !important;
    font-size: 0.8rem !important;
    min-width: 50px !important;
    height: 26px !important;
  }
}

@media (max-width: 768px) {
  .el-table .el-table__cell {
    padding: 4px 3px !important;
    font-size: 0.85rem !important;
  }

  .btn-blue-light.compact,
  .btn-green-light.compact {
    padding: 2px 4px !important;
    font-size: 0.75rem !important;
    min-width: 45px !important;
    height: 24px !important;
  }

  .btn-blue-light.compact span,
  .btn-green-light.compact span {
    display: none !important;
  }
}

/* 移除重复的表格滚动条样式 */

/* 统计卡片和卡片布局 */
.erp-stats-row {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 14px !important;
  margin-bottom: 18px !important;
  width: 100% !important;
}

.erp-stat-card {
  background: #fff !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03) !important;
  padding: 16px 14px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  position: relative !important;
  min-height: 80px !important;
  overflow: hidden !important;
  transition: box-shadow 0.18s !important;
}

.erp-stat-label {
  color: #64748b !important;
  font-size: 0.92rem !important;
  margin-bottom: 4px !important;
}

.erp-stat-value {
  font-size: 1.32rem !important;
  font-weight: 700 !important;
  color: #22223b !important;
  margin-bottom: 4px !important;
  letter-spacing: 0.2px !important;
}

.erp-stat-icon {
  position: absolute !important;
  right: 14px !important;
  bottom: 14px !important;
  font-size: 1.6rem !important;
  opacity: 0.8 !important;
}

.erp-stat-icon.blue { color: #2563eb !important; }
.erp-stat-icon.green { color: #10b981 !important; }
.erp-stat-icon.purple { color: #7c3aed !important; }
.erp-stat-icon.red { color: #ef4444 !important; }

/* 通用卡片样式 */
.erp-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03) !important;
  margin-bottom: 24px !important;
  background: #fff !important;
  padding: 18px 18px 22px 18px !important;
  width: 100% !important;
}

/* 表格工具栏样式 */
.erp-table-toolbar {
  display: flex !important;
  flex-direction: column !important;
  gap: 10px !important;
  margin-bottom: 12px !important;
  align-items: flex-start !important;
}

/* 删除表格响应式滚动条 - 第七处 */

/* 页面容器样式 */
.erp-dashboard-page,
.erp-hr-page,
.erp-warehouse-page {
  padding-bottom: 20px !important;
  width: 100% !important;
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
  .erp-stats-row {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .erp-table-responsive {
    margin: 0 !important;
    width: 100% !important;
    padding: 0 !important;
  }
  
  .erp-card {
    padding: 16px 10px 18px 10px !important;
  }
  
  .erp-stats-row {
    grid-template-columns: 1fr !important;
  }
}

/* 表格边框和斑马线样式 */
.el-table--border,
.el-table--group,
.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: #e5e7eb !important;
}

/* 表格悬停效果 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #f3f4f6 !important;
}

/* 固定列样式 */
.el-table .el-table__fixed,
.el-table .el-table__fixed-right {
  box-shadow: var(--shadow-sm) !important;
  z-index: 2 !important;
  background-color: white !important;
  height: 100% !important;
}

.el-table .el-table__fixed-right::before,
.el-table .el-table__fixed::before {
  background-color: #e5e7eb !important;
}

/* 确保所有表格行高一致 */
.el-table__row {
  height: 48px !important;
}

/* 全局按钮样式 */
.btn-blue {
  background-color: #2563eb;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-blue:hover {
  background-color: #1d4ed8;
}

.btn-green {
  background-color: #16a34a;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-green:hover {
  background-color: #15803d;
}

.btn-red {
  background-color: #dc2626;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-red:hover {
  background-color: #b91c1c;
}

.btn-gray {
  background-color: #f3f4f6;
  color: #374151;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-gray:hover {
  background-color: #e5e7eb;
}

.btn-yellow {
  background-color: #eab308;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-yellow:hover {
  background-color: #ca8a04;
}

.btn-purple {
  background-color: #a78bfa;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-purple:hover {
  background-color: #7c3aed;
}

/* 浅色按钮（带色文字） */
.btn-blue-light {
  background-color: #dbeafe;
  color: #2563eb;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-blue-light:hover {
  background-color: #bfdbfe;
}

.btn-green-light {
  background-color: #dcfce7;
  color: #16a34a;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-green-light:hover {
  background-color: #bbf7d0;
}

.btn-orange-light {
  background-color: #fed7aa;
  color: #ea580c;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-orange-light:hover {
  background-color: #fdba74;
}

/* 文本颜色工具类 */
.text-red-600 {
  color: #dc2626 !important;
}

.text-orange-600 {
  color: #ea580c !important;
}

.text-green-600 {
  color: #16a34a !important;
}

.btn-red-light {
  background-color: #fee2e2;
  color: #dc2626;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-red-light:hover {
  background-color: #fecaca;
}

.btn-yellow-light {
  background-color: #fef9c3;
  color: #eab308;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-yellow-light:hover {
  background-color: #fef08a;
}

.btn-purple-light {
  background-color: #f3e8ff;
  color: #a78bfa;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}
.btn-purple-light:hover {
  background-color: #e9d5ff;
}

/* 输入框常驻灰色边框，无高亮、无黑色激活边框 */
.el-input__wrapper {
  border: 1.5px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: none !important;
}
.el-input__wrapper:hover, .el-select .el-input__wrapper:hover, .el-date-editor.el-input__wrapper:hover{
  border-color: #d1d5db !important;
}
.el-input__wrapper:focus-within, .el-select .el-input__wrapper:focus-within, .el-date-editor.el-input__wrapper:focus-within{
  box-shadow: none !important;
}
.el-input__wrapper:focus-within, .el-select .el-input__wrapper:focus-within, .el-date-editor.el-input__wrapper:focus-within{
  border-color: #d1d5db !important;
}
.el-input__inner {
  box-shadow: none !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  vertical-align: middle;
}

/* 删除 .el-tabs__header.is-top 的下边框样式 */
.el-tabs__header.is-top {
  border-bottom: none !important;
  box-shadow: none !important;
  margin-bottom: 0 !important;
  margin: 0 !important;
}

/* 修复使用Tailwind类的按钮边框问题 */
[class*="bg-"][class*="hover:bg-"][class*="text-"][class*="font-bold"][class*="py-"][class*="px-"][class*="rounded-"],
.bg-blue-100, .bg-blue-600, .bg-red-100, .bg-red-600, .bg-green-100, .bg-green-600, 
.bg-yellow-100, .bg-yellow-500, .bg-purple-100, .bg-gray-100 {
  border: none !important;
  box-shadow: var(--shadow-sm);
}

/* 修复特定Tailwind类组合的按钮 */
.bg-blue-100.hover\:bg-blue-200.text-blue-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2,
.bg-red-100.hover\:bg-red-200.text-red-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2,
.bg-green-100.hover\:bg-green-200.text-green-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2,
.bg-yellow-100.hover\:bg-yellow-200.text-yellow-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2,
.bg-purple-100.hover\:bg-purple-200.text-purple-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2,
.bg-gray-100.hover\:bg-gray-200.text-gray-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 {
  border: none !important;
  box-shadow: var(--shadow-sm);
}

/* 修复这些按钮中的Font Awesome图标垂直居中问题 - 使用transform微调位置 */
.bg-blue-100.hover\:bg-blue-200.text-blue-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i,
.bg-red-100.hover\:bg-red-200.text-red-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i,
.bg-green-100.hover\:bg-green-200.text-green-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i,
.bg-yellow-100.hover\:bg-yellow-200.text-yellow-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i,
.bg-purple-100.hover\:bg-purple-200.text-purple-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i,
.bg-gray-100.hover\:bg-gray-200.text-gray-700.font-bold.py-2.px-4.rounded-lg.shadow-sm.transition.duration-200.ease-in-out.flex.items-center.justify-center.space-x-2 i {
  transform: translateY(2px) !important;
}

/* 全局按钮样式 */
.btn-blue {
  background-color: #2563eb;
  color: #fff;
  font-weight: 700;
  padding: 0.5rem 1.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  transition: background 0.2s cubic-bezier(0.4,0,0.2,1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}

/* 强制所有表格行hover变色，优先级最高 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #f3f4f6 !important;
  transition: background 0.18s;
  z-index: 1;
}

/* 统一所有表格单元格底色，覆盖所有情况（包括固定列、斑马纹等） */
.el-table__body td.el-table__cell,
.el-table .el-table__body td.el-table__cell,
.el-table__cell,
.el-table .el-table__cell,
.el-table .el-table__fixed-right .el-table__cell,
.el-table .el-table__fixed .el-table__cell {
  background: #fff !important;
  background-color: #fff !important;
}
/* 强制所有表格行hover变色，覆盖所有情况（包括固定列） */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell,
.el-table .el-table__body tr:hover > td.el-table__cell,
.el-table__body tr:hover > td.el-table__cell,
.el-table .el-table__fixed-right tr:hover > td.el-table__cell,
.el-table .el-table__fixed tr:hover > td.el-table__cell {
  background: #f3f4f6 !important;
  background-color: #f3f4f6 !important;
  transition: background 0.18s;
  z-index: 1;
}

/* === 输入框/下拉等控件自适应宽度 === */
.el-input, .el-select, .el-date-picker {
  min-width: 0 !important;
  max-width: 220px !important;
  box-sizing: border-box !important;
}

/* Element Plus 日期选择器弹出面板样式修复 */
.el-picker-panel,
.el-date-picker__editor-wrap,
.el-picker__popper,
.el-popper.is-pure {
  min-width: auto !important;
  max-width: none !important;
  width: auto !important;
}

/* 月份选择器面板特定样式 */
.el-month-table,
.el-picker-panel .el-month-table {
  min-width: 280px !important;
  width: 280px !important;
}

/* 确保弹出面板不受父容器宽度限制 */
.el-picker__popper .el-picker-panel {
  min-width: 280px !important;
  width: auto !important;
}

/* 日期范围选择器宽度优化 */
.el-date-editor--daterange,
.el-date-editor.el-input__wrapper.el-range-editor,
.first-row .el-date-editor--daterange,
.erp-table-toolbar .el-date-editor--daterange,
.erp-table-toolbar .el-date-editor.el-input__wrapper.el-range-editor {
  max-width: 240px !important;
  width: 240px !important;
  box-sizing: border-box !important;
}

/* 日期选择器弹窗大小优化 */
.el-picker-panel {
  max-width: 600px !important;
  width: auto !important;
}

.el-date-range-picker {
  max-width: 600px !important;
  width: auto !important;
}

/* 日期选择器内部表格优化 */
.el-picker-panel .el-date-table,
.el-picker-panel .el-month-table,
.el-picker-panel .el-year-table {
  font-size: 12px !important;
}

.el-picker-panel .el-date-table td {
  width: 28px !important;
  height: 28px !important;
  padding: 2px !important;
}


/* 添加first-row和second-row样式，防止按钮被挤到第二行 */
.first-row, .second-row {
  display: flex !important;
  flex-direction: row !important;
  gap: 8px !important;
  align-items: center !important;
  justify-content: flex-start !important;
  width: 100% !important;
  padding-bottom: 4px !important;
  /* 添加溢出处理 */
  overflow-x: auto !important;
  overflow-y: hidden !important;
  flex-wrap: nowrap !important;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE */
}

/* 隐藏 Webkit 滚动条 */
.first-row::-webkit-scrollbar,
.second-row::-webkit-scrollbar {
  display: none !important;
}

.second-row {
  flex-wrap: wrap !important;
  overflow-x: visible !important; /* second-row 允许换行，不需要横向滚动 */
}

.first-row .el-input,
.first-row .el-select,
.first-row .el-date-picker,
.first-row .el-date-editor {
  width: 180px !important;
  min-width: 120px !important;
  max-width: 220px !important;
  flex: 0 0 auto !important;
  height: auto;
}

/* 为日期范围选择器设置合适的宽度 */
.first-row .el-date-editor--daterange,
.first-row .el-date-editor.el-input__wrapper.el-range-editor {
  width: 260px !important;
  max-width: 260px !important;
  flex: 0 0 auto !important;
  box-sizing: border-box !important;
}

.first-row button,
.second-row button {
  flex: 0 0 auto !important;
  white-space: nowrap !important;
  min-width: 80px !important;
}

/* 媒体查询适配中等屏幕 */
@media (max-width: 1200px) {
  /* 中等屏幕下优化宽度，防止溢出 */
  .first-row .el-input,
  .first-row .el-select,
  .first-row .el-date-picker,
  .first-row .el-date-editor {
    min-width: 110px !important;
    max-width: 180px !important;
  }

  .first-row .el-date-editor--daterange,
  .first-row .el-date-editor.el-input__wrapper.el-range-editor {
    width: 240px !important;
    max-width: 240px !important;
  }
}

/* 媒体查询适配小屏幕 */
@media (max-width: 768px) {
  .first-row, .second-row {
    gap: 6px !important;
  }
  .first-row .el-input,
  .first-row .el-select,
  .first-row .el-date-picker,
  .first-row .el-date-editor {
    min-width: 100px !important;
    max-width: 180px !important;
  }
  .first-row .el-date-editor--daterange,
  .first-row .el-date-editor.el-input__wrapper.el-range-editor {
    width: 220px !important;
    max-width: 220px !important;
  }
  .first-row button,
  .second-row button {
    min-width: 70px !important;
  }
}

@media (max-width: 480px) {
  .first-row, .second-row {
    gap: 4px !important;
  }
  .first-row .el-input,
  .first-row .el-select,
  .first-row .el-date-picker,
  .first-row .el-date-editor {
    min-width: 90px !important;
    max-width: 140px !important;
  }
  .first-row .el-date-editor--daterange,
  .first-row .el-date-editor.el-input__wrapper.el-range-editor {
    width: 180px !important;
    max-width: 180px !important;
  }
  .first-row button,
  .second-row button {
    min-width: 60px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

/* ========================================
   ERP 标准表格组件 - 完美解决方案
   ======================================== */

/* 表格容器 */
.erp-standard-table {
  margin-top: 16px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 核心修复：强制表格使用单一表格布局 */
.erp-standard-table .el-table {
  border: none !important; /* 移除内部边框，由容器提供 */
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* 关键修复：禁用Element Plus的分离式表头/表体布局 */
.erp-standard-table .el-table__header-wrapper,
.erp-standard-table .el-table__body-wrapper {
  overflow: visible !important;
}

/* 强制表格使用统一的表格布局算法 */
.erp-standard-table .el-table,
.erp-standard-table .el-table__header,
.erp-standard-table .el-table__body {
  table-layout: auto !important;
  width: 100% !important;
}

/* 禁用Element Plus的列宽预设 */
.erp-standard-table .el-table colgroup {
  display: none !important;
}

/* 表头样式 */
.erp-data-table .el-table__header th.el-table__cell {
  background-color: #f9fafb;
  color: #1f2937;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  text-align: left;
}

/* 表格单元格样式 - 支持内容换行 */
.erp-data-table .el-table__cell {
  border-bottom: 1px solid #e5e7eb;
  vertical-align: middle;
  white-space: normal !important; /* 允许换行 */
  word-wrap: break-word !important; /* 长单词自动换行 */
  word-break: break-all !important; /* 必要时断词换行 */
  min-width: 80px !important; /* 设置最小宽度，确保内容可读 */
  padding: 8px 12px !important; /* 适当的内边距 */
}

/* 表头和表体列宽强制同步 */
.erp-data-table .el-table__header th,
.erp-data-table .el-table__body td {
  width: auto !important;
  box-sizing: border-box !important;
}

/* 使用CSS Grid强制表头和内容列宽同步 */
.erp-data-table .el-table {
  display: block !important;
}

.erp-data-table .el-table__header-wrapper,
.erp-data-table .el-table__body-wrapper {
  display: block !important;
  overflow: visible !important;
}

.erp-data-table .el-table__header,
.erp-data-table .el-table__body {
  display: table !important;
  width: 100% !important;
  table-layout: auto !important;
  border-collapse: separate !important;
}

.erp-data-table .el-table__header tr,
.erp-data-table .el-table__body tr {
  display: table-row !important;
}

.erp-data-table .el-table__header th,
.erp-data-table .el-table__body td {
  display: table-cell !important;
}

/* 移除特定列的最小宽度限制，让JavaScript动态计算生效 */

/* 操作按钮容器 */
.erp-data-table .action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: nowrap;
}

/* ===== 全局表格过渡动画 ===== */
/* 表格列宽过渡动画 - 适用于所有Element Plus表格 */
.el-table th,
.el-table td {
  transition: width 0.3s ease-in-out, min-width 0.3s ease-in-out, max-width 0.3s ease-in-out !important;
}

/* 表头过渡动画 */
.el-table__header {
  transition: width 0.3s ease-in-out, min-width 0.3s ease-in-out !important;
}

/* 表格内容过渡动画 */
.el-table__body {
  transition: width 0.3s ease-in-out !important;
}

/* 自定义滚动条过渡动画 */
.custom-scrollbar-horizontal {
  transition: opacity 0.3s ease-in-out !important;
}

.custom-scrollbar-thumb {
  transition: width 0.2s ease-in-out, left 0.2s ease-in-out !important;
}

/* ===== 表格选择列（checkbox）点击区域扩大 ===== */
/* 扩大选择列的整个单元格点击区域 */
.el-table .el-table-column--selection .cell {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  cursor: pointer !important;
}

/* 扩大checkbox的点击区域到整个单元格 */
.el-table .el-table-column--selection .el-checkbox {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

/* 扩大checkbox输入框的点击区域 */
.el-table .el-table-column--selection .el-checkbox__input {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 扩大checkbox原生input的点击区域 */
.el-table .el-table-column--selection .el-checkbox__original {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  opacity: 0 !important;
  cursor: pointer !important;
  z-index: 2 !important;
}

/* 保持checkbox视觉样式不变，但扩大点击区域 */
.el-table .el-table-column--selection .el-checkbox__inner {
  position: relative !important;
  z-index: 1 !important;
  pointer-events: none !important; /* 禁用原有的点击事件，让原生input处理 */
}

/* 移除选择列单元格hover效果，保持原样 */

/* 确保选择列的宽度固定，并移除上下边距 */
.el-table .el-table-column--selection {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

/* 移除选择列的上下边距，让点击区域更大 */
.el-table .el-table-column--selection.el-table__cell,
.el-table .el-table__header .el-table-column--selection.el-table__cell,
.el-table .el-table__body .el-table-column--selection.el-table__cell {
  padding: 0 !important;
}

/* 确保ErpTable中的选择列也移除边距 */
.erp-data-table .el-table .el-table-column--selection.el-table__cell,
.erp-data-table .el-table__header .el-table-column--selection.el-table__cell,
.erp-data-table .el-table__body .el-table-column--selection.el-table__cell {
  padding: 0 !important;
}

/* 选择列表头也应用相同的样式 */
.el-table .el-table__header .el-table-column--selection .cell {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  cursor: pointer !important;
}

.el-table .el-table__header .el-table-column--selection .el-checkbox {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.el-table .el-table__header .el-table-column--selection .el-checkbox__input {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.el-table .el-table__header .el-table-column--selection .el-checkbox__original {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  opacity: 0 !important;
  cursor: pointer !important;
  z-index: 2 !important;
}

.el-table .el-table__header .el-table-column--selection .el-checkbox__inner {
  position: relative !important;
  z-index: 1 !important;
  pointer-events: none !important;
}

/* 移除表头选择列单元格hover效果，保持原样 */

/* === 强制覆盖弹窗标签字体 - 最高优先级 === */
.el-dialog .el-dialog__body .el-form-item__label,
.el-dialog__body .el-form-item__label,
body .el-dialog .el-dialog__body .el-form-item__label {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
}